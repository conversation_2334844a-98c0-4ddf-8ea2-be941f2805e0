/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * common-tab.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import { Badge } from '@libra/ui/components/badge'

interface ComingSoonTabProps {
  title: string
  description: string
}

/**
 * General "Coming Soon" feature tab
 */
export function ComingSoonTab({ title, description }: ComingSoonTabProps) {
  return (
    <div className='h-full flex items-center justify-center p-8'>
      <div className='w-full max-w-2xl text-center space-y-8'>
        {/* Header */}
        <div className='space-y-4'>
          <h2 className='text-2xl font-bold tracking-tight text-foreground'>
            {title}
          </h2>
          <p className='text-muted-foreground text-lg'>
            {description}
          </p>
        </div>

        {/* Coming Soon Card */}
        <div className='bg-gradient-to-br from-primary/5 via-background to-blue-500/5 border border-border/50 rounded-2xl p-12 shadow-sm'>
          <div className='space-y-6'>
            {/* Icon placeholder */}
            <div className='w-16 h-16 bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-2xl mx-auto flex items-center justify-center'>
              <div className='w-8 h-8 bg-gradient-to-br from-primary to-blue-500 rounded-lg' />
            </div>

            {/* Badge */}
            <Badge variant='outline' className='bg-background/80 text-primary border-primary/30 px-4 py-2 text-sm font-medium'>
              Coming Soon
            </Badge>

            {/* Description */}
            <div className='space-y-2'>
              <p className='text-foreground font-medium'>
                {title} feature is in development
              </p>
              <p className='text-muted-foreground text-sm leading-relaxed max-w-md mx-auto'>
                We're working hard to bring you this feature. Stay tuned for updates and new capabilities!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}