/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * common-tab.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import { Badge } from '@libra/ui/components/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@libra/ui/components/card'

interface ComingSoonTabProps {
  title: string
  description: string
}

/**
 * General "Coming Soon" feature tab
 */
export function ComingSoonTab({ title, description }: ComingSoonTabProps) {
  return (
    <div className='h-full flex items-center justify-center'>
      <Card className='w-full max-w-2xl'>
        <CardHeader className='pb-4'>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-center h-64 border rounded-md bg-muted/20'>
            <div className='text-center p-6'>
              <Badge variant='outline' className='mb-3'>
                Coming Soon
              </Badge>
              <p className='text-muted-foreground text-sm'>{title} feature coming soon, stay tuned!</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}