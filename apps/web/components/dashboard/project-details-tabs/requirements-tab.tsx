/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * requirements-tab.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import type { FormState } from '../project-details-types'
import { Textarea } from '@libra/ui/components/textarea'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@libra/ui/components/card'
import * as m from '@/paraglide/messages'

interface RequirementsTabProps {
  formState: FormState
  onFormChange: (field: 'name' | 'description' | 'knowledge', value: string) => void
}

/**
 * Knowledge tab component
 */
export function RequirementsTab({ formState, onFormChange }: RequirementsTabProps) {
  return (
    <div className='h-full flex flex-col'>
      <Card className='flex-1 min-h-0 flex flex-col'>
        <CardHeader className='flex-shrink-0 pb-3'>
          <CardTitle>{m["dashboard.projectDetailsTabs.knowledge.title"]()}</CardTitle>
          <CardDescription>
            {m["dashboard.projectDetailsTabs.knowledge.description"]()}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col space-y-3 min-h-0">
          <Textarea
            value={formState.knowledge}
            onChange={(e) => onFormChange('knowledge', e.target.value)}
            className='flex-1 min-h-[200px] resize-none'
            placeholder={m["dashboard.projectDetailsTabs.knowledge.placeholder"]()}
          />
          <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md flex-shrink-0">
            <p className="font-medium mb-1">{m["dashboard.projectDetailsTabs.knowledge.tipTitle"]()}</p>
            <p>{m["dashboard.projectDetailsTabs.knowledge.tipContent"]()}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}