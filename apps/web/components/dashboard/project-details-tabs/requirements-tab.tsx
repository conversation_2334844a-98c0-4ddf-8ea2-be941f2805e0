/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * requirements-tab.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import { useId } from 'react'
import type { FormState } from '../project-details-types'
import { Textarea } from '@libra/ui/components/textarea'
import * as m from '@/paraglide/messages'

interface RequirementsTabProps {
  formState: FormState
  onFormChange: (field: 'name' | 'description' | 'knowledge', value: string) => void
}

/**
 * Knowledge tab component
 */
export function RequirementsTab({ formState, onFormChange }: RequirementsTabProps) {
  const knowledgeId = useId()

  return (
    <div className='h-full space-y-8'>
      {/* Header Section */}
      <div className='space-y-3'>
        <h2 className='text-xl font-bold tracking-tight text-foreground'>
          {m["dashboard.projectDetailsTabs.knowledge.title"]()}
        </h2>
        <p className='text-sm text-muted-foreground'>
          {m["dashboard.projectDetailsTabs.knowledge.description"]()}
        </p>
        <div className='h-px bg-gradient-to-r from-border to-transparent' />
      </div>

      {/* Knowledge Input Section */}
      <div className='bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 p-8 shadow-sm space-y-6'>
        <div className='space-y-4'>
          <label htmlFor={knowledgeId} className='text-sm font-semibold text-foreground block'>
            Project Knowledge & Requirements
          </label>
          <Textarea
            id={knowledgeId}
            value={formState.knowledge}
            onChange={(e) => onFormChange('knowledge', e.target.value)}
            className='min-h-[300px] bg-background/80 border-border/60 focus:border-primary/80 focus:ring-2 focus:ring-primary/20 transition-all duration-200 text-base leading-relaxed resize-none'
            placeholder={m["dashboard.projectDetailsTabs.knowledge.placeholder"]()}
          />
        </div>

        {/* Help Section */}
        <div className="bg-gradient-to-r from-primary/5 to-blue-500/5 border border-primary/20 rounded-xl p-6">
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0" />
            <div className="space-y-2">
              <p className="font-semibold text-foreground text-sm">
                {m["dashboard.projectDetailsTabs.knowledge.tipTitle"]()}
              </p>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {m["dashboard.projectDetailsTabs.knowledge.tipContent"]()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}