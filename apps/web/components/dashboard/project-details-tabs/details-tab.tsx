/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * details-tab.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import { useId } from 'react'
import type { FormState } from '../project-details-types'
import { formatDate } from '../use-project-details'
import { Input } from '@libra/ui/components/input'
import { Badge } from '@libra/ui/components/badge'
import { Textarea } from '@libra/ui/components/textarea'
import * as m from '@/paraglide/messages'

interface DetailsTabProps {
  project: any // The specific project type should be defined here
  formState: FormState
  projectId: string | null
  onFormChange: (field: 'name' | 'description' | 'knowledge', value: string) => void
}

/**
 * Project details tab - redesigned with flat layout
 */
export function DetailsTab({
  project,
  formState,
  projectId,
  onFormChange
}: DetailsTabProps) {
  const projectNameId = useId()
  const projectDescriptionId = useId()
  
  return (
    <div className='space-y-10 h-full'>
      {/* Basic Information Section */}
      <section className='space-y-6'>
        <div className='space-y-3'>
          <h2 className='text-xl font-bold tracking-tight text-foreground'>
            {m["dashboard.projectDetailsTabs.details.basicInfo"]()}
          </h2>
          <p className='text-sm text-muted-foreground'>
            Manage your project's basic information and settings
          </p>
          <div className='h-px bg-gradient-to-r from-border to-transparent' />
        </div>

        <div className='bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 p-8 space-y-8 shadow-sm'>
          {/* Project Name Field */}
          <div className='space-y-4'>
            <label htmlFor={projectNameId} className='text-sm font-semibold text-foreground flex items-center gap-2'>
              {m["dashboard.projectDetailsTabs.details.projectName"]()}
              <span className='text-primary'>*</span>
            </label>
            <Input
              id={projectNameId}
              value={formState.name}
              onChange={(e) => onFormChange('name', e.target.value)}
              className='h-12 bg-background/80 border-border/60 focus:border-primary/80 focus:ring-2 focus:ring-primary/20 transition-all duration-200 text-base'
              placeholder={m["dashboard.projectDetailsTabs.details.projectNamePlaceholder"]()}
            />
          </div>

          {/* Project Description Field */}
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <label htmlFor={projectDescriptionId} className='text-sm font-semibold text-foreground'>
                {m["dashboard.projectDetailsTabs.details.projectDescription"]()}
              </label>
              <Badge variant='secondary' className='bg-muted/60 text-muted-foreground text-xs px-3 py-1'>
                {m["dashboard.projectDetailsTabs.details.readOnly"]()}
              </Badge>
            </div>
            <Textarea
              id={projectDescriptionId}
              value={formState.description}
              className='min-h-[140px] bg-background/80 border-border/60 resize-none text-base leading-relaxed'
              readOnly
              placeholder={m["dashboard.projectDetailsTabs.details.projectDescriptionPlaceholder"]()}
            />
            <p className='text-sm text-muted-foreground leading-relaxed bg-muted/30 rounded-lg p-3 border-l-4 border-primary/30'>
              {m["dashboard.projectDetailsTabs.details.projectDescriptionHelp"]()}
            </p>
          </div>
        </div>
      </section>

      {/* Project Information Section */}
      <section className='space-y-6'>
        <div className='space-y-3'>
          <h2 className='text-xl font-bold tracking-tight text-foreground'>
            {m["dashboard.projectDetailsTabs.details.projectInfo"]()}
          </h2>
          <p className='text-sm text-muted-foreground'>
            View technical details and metadata about your project
          </p>
          <div className='h-px bg-gradient-to-r from-border to-transparent' />
        </div>

        <div className='bg-card/30 backdrop-blur-sm rounded-2xl border border-border/50 p-8 shadow-sm'>
          <dl className='space-y-6'>
            <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 py-4 border-b border-border/40'>
              <dt className='text-sm font-semibold text-muted-foreground flex items-center gap-2'>
                <div className='w-2 h-2 rounded-full bg-green-500' />
                {m["dashboard.projectDetailsTabs.details.createdAt"]()}
              </dt>
              <dd className='text-sm font-medium text-foreground bg-muted/40 px-3 py-2 rounded-lg'>
                {formatDate(typeof project === 'object' ? project?.createdAt : null)}
              </dd>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 py-4 border-b border-border/40'>
              <dt className='text-sm font-semibold text-muted-foreground flex items-center gap-2'>
                <div className='w-2 h-2 rounded-full bg-blue-500' />
                {m["dashboard.projectDetailsTabs.details.lastUpdated"]()}
              </dt>
              <dd className='text-sm font-medium text-foreground bg-muted/40 px-3 py-2 rounded-lg'>
                {formatDate(typeof project === 'object' ? project?.updatedAt : null)}
              </dd>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 py-4'>
              <dt className='text-sm font-semibold text-muted-foreground flex items-center gap-2'>
                <div className='w-2 h-2 rounded-full bg-purple-500' />
                {m["dashboard.projectDetailsTabs.details.projectId"]()}
              </dt>
              <dd>
                <code className='text-xs bg-muted/60 text-muted-foreground px-4 py-2 rounded-lg font-mono border border-border/40 select-all'>
                  {projectId}
                </code>
              </dd>
            </div>
          </dl>
        </div>
      </section>
    </div>
  )
} 
